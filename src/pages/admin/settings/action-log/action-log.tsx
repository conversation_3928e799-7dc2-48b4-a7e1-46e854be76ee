import React, { FC, useMemo } from 'react'
import { IActionLogItem } from '@/shared/types/store/settings'

import classNamesBind from 'classnames/bind'
import styles from './action-log.module.scss'

import { <PERSON><PERSON><PERSON><PERSON>, But<PERSON>, Loader, Pagination } from '@/shared/ui'
import BoxIcon from '@/shared/ui/Icon/icons/components/BoxIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { Tabs } from '@/shared/components'
import { DOT_DATE_FORMAT, format, TIME_FORMAT } from '@/shared/helpers/date'
import { PAGE_ITEMS_LIMIT } from '@/shared/constants/index'

import { useGetTabs } from './data'
import { ActionLogModal } from './modal/action-log-modal'
import { useActionLog } from './use-action-log'
import { ActionLogProps } from './action-log.d'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

//! Заглушка для таблицы, когда данных нет
const EmptyPlug = () => {
  const { t } = useTranslation()

  return (
    <div className={cx('plug__container')}>
      <div className={cx('plug__icon', 'box_icon')}>
        <IconWrapper color='gray60'>
          <BoxIcon />
        </IconWrapper>
      </div>
      <h2 className={cx('plug__text')}>{t('commons:no_data')}</h2>
    </div>
  )
}

interface ITableRow {
  row: IActionLogItem
}

const ACTION_LOG_NAMESPACE = 'pages__action-log'

const TableRow: React.FC<ITableRow> = ({ row }) => {
  const { t } = useTranslation()
  const { t: tLog } = useTranslation(ACTION_LOG_NAMESPACE)

  const rowDetails = Object.entries(row?.details ?? {})
    .map(([key, value]) => `${tLog(key)}: ${value}`)
    .join(', ')

  return (
    <tr key={row.id}>
      <td className={cx('date-cell')}>
        <p className={cx('date')}>{format(new Date(row.created_at), DOT_DATE_FORMAT)}</p>
        <p className={cx('time')}>{format(new Date(row.created_at), TIME_FORMAT)}</p>
      </td>
      <td className={cx('action-cell')}>{tLog(row.action, { ns: ACTION_LOG_NAMESPACE })}</td>
      <td className={cx('employee-cell')}>
        {row.user_name && <p title={row.user_name}>{row.user_name}</p>}
        {row.user_email && (
          <p title={row.user_email} className={cx('email')}>
            {row.user_email}
          </p>
        )}
        {!row.user_name && !row.user_email && row?.details?.user_id && (
          <p title={row?.details?.user_id}>{row?.details?.user_id}</p>
        )}
      </td>
      <td className={cx('details-cell')}>
        {row.details ? (
          <p title={rowDetails}>{rowDetails}</p>
        ) : (
          <p className={cx('no-info')}>{t('commons:no_information')}</p>
        )}
      </td>
      <td className={cx('ip-cell')}>
        {row.ip && (
          // eslint-disable-next-line i18next/no-literal-string
          <p title={row.ip} className={cx('ip')}>
            IP: {row.ip}
          </p>
        )}
        {row.browser && (
          <p title={row.browser} className={cx('user-agent')}>
            {row.browser}
          </p>
        )}
        {row.os && (
          <p title={row.os} className={cx('user-agent')}>
            {row.os}
          </p>
        )}
      </td>
    </tr>
  )
}

export const ActionLog: FC<ActionLogProps.Props> = props => {
  const { className } = props
  const {
    data,
    ACTUAL_TAB,
    type,
    tableHeaderRef,
    ACTUAL_PAGE,
    isEmptyData,
    isPaginationVisible,
    onSetPage,
    onTabChange,
    modalOpen,
    setModalOpen,
    isExportDisabled,
    isFetching,
    wasError,
    isLoading,
  } = useActionLog()

  const { t } = useTranslation()

  const TABS = useGetTabs()

  const TABLE_COLS = useMemo(
    () => [
      {
        label: t('commons:date').toUpperCase(),
        key: t('commons:date'),
      },
      {
        label: t('commons:action').toUpperCase(),
        key: t('commons:action'),
      },
      {
        label: t('commons:employee_role').toUpperCase(),
        key: t('commons:employee_role'),
      },
      {
        label: t('commons:details').toUpperCase(),
        key: t('commons:details'),
      },
      {
        label: 'IP',
        key: 'IP',
      },
    ],
    [t],
  )

  return (
    <div className={cx('container', className)}>
      <PageTitle>{t('commons:audit')}</PageTitle>
      <div className={cx('content__wrapper')}>
        <div className={cx('actions__container')}>
          <Tabs tabs={TABS} active={type} onClick={onTabChange} />
          <Button
            onClick={() => {
              setModalOpen(true)
            }}
            disabled={isExportDisabled}
            size='small'
            color='gray'
            rightIcon={'download'}
            className={cx('btn__export', {
              disabled: isExportDisabled,
            })}
          >
            {t('commons:export_excel')}
          </Button>
        </div>
        <table className={cx('table', isFetching && 'table__opacity', className)}>
          <thead ref={tableHeaderRef}>
            <tr>
              {TABLE_COLS.map(column => (
                <th title={column.label} key={column.key}>
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {isLoading && (
              <tr>
                <td colSpan={TABLE_COLS.length}>
                  <Loader size='56' className={cx('loader', 'loader_centered')} />
                </td>
              </tr>
            )}
            {!isLoading && isFetching ? (
              wasError ? (
                <tr key={'row-first-loading-plug'}>
                  <td colSpan={TABLE_COLS.length}>
                    <div className={cx('plug__container')}>
                      <Loader size='56' />
                    </div>
                  </td>
                </tr>
              ) : (
                <tr className={cx('plug__loader__container')} key={'row-loading-plug'}>
                  <td className={cx('plug__loader')} colSpan={TABLE_COLS.length}>
                    <Loader size='56' />
                  </td>
                </tr>
              )
            ) : wasError ? (
              <tr key={'row-error-plug'}>
                <td colSpan={TABLE_COLS.length}>
                  <div className={cx('plug__container')}>
                    <Loader error loading={false} size='56' />
                    <h2 className={cx('plug__text')}>{t('commons:error_with_dots')}</h2>
                  </div>
                </td>
              </tr>
            ) : isEmptyData ? (
              <tr key={'row-empty-plug'}>
                <td colSpan={TABLE_COLS.length}>
                  <EmptyPlug />
                </td>
              </tr>
            ) : null}
            {!wasError && data?.data?.map(row => <TableRow key={row.id} row={row} />)}
          </tbody>
        </table>
      </div>
      {isPaginationVisible && (
        <Pagination
          currentPage={ACTUAL_PAGE}
          limit={PAGE_ITEMS_LIMIT}
          onChange={onSetPage}
          total={data?.total_count || 0}
          isLoading={isLoading || isFetching}
        />
      )}
      {modalOpen && (
        <ActionLogModal
          type={type}
          modalOpen={modalOpen}
          setModalOpen={setModalOpen}
          title={`${t('commons:export')} "${ACTUAL_TAB?.value}"`}
        />
      )}
    </div>
  )
}

export default ActionLog

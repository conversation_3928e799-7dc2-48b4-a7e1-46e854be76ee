@use './../../../../shared/assets/styles/mixins/icons';
@use './../../../../shared/assets/styles/mixins/text';

.container {
  display: flex;
  flex-direction: column;
}

.actions {
  &__container {
    align-items: center;
    display: flex;
    justify-content: space-between;
  }
}

.title {
  color: var(--color-gray-90);
  font: var(--font-title-2-medium);
}

.type-tabs {
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-top: 28px;
}

.table {
  background-color: var(--white);
  border-radius: 16px;
  padding: 12px 16px;
  width: 100%;

  tbody {
    color: var(--color-gray-90);
    font: var(--font-caption-1-medium);
    position: relative;
    .date-cell {
      width: 15%;
    }
    .action-cell {
      width: 20%;
    }
    .employee-cell {
      width: 20%;
    }
    .details-cell {
      width: 25%;
    }
    .ip-cell {
      width: 20%;
    }
    tr {
      border-top: 1px solid var(--color-gray-30);
      padding: 12px 16px;
    }
    td {
      padding: 12px 16px;

      vertical-align: middle;

      p,
      span {
        @include text.max-lines(3);
        text-transform: capitalize;
      }
    }

    .date {
      color: var(--color-gray-80);
      font: var(--font-caption-1-medium);
    }
    .time {
      color: var(--color-gray-70);
      font: var(--font-caption-1-normal);
      margin-top: 4px;
    }
    .email {
      color: var(--color-gray-80);
      font: var(--font-caption-2-normal);
    }
    .user-agent {
      color: var(--color-gray-80);
      font: var(--font-caption-2-normal);
    }
    .no-info {
      color: var(--color-gray-70);

      font: var(--font-caption-2-medium);
    }

    .ip + .user-agent {
      margin-top: 4px;
    }
  }
  thead {
    color: var(--color-gray-70);

    font: var(--font-caption-1-medium);
    padding: 12px 16px;

    th {
      align-items: flex-start;
      flex-direction: column;
      justify-content: center;
      padding: 12px 16px;

      text-align: start;
    }
  }

  &__opacity {
    opacity: 0.7;
  }
}

.content__wrapper {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.btn__export {
  svg,
  path {
    fill: var(--color-gray-70) !important;
  }
  &:hover {
    svg,
    path {
      fill: var(--color-gray-80) !important;
    }
  }
  &.disabled {
    svg,
    path {
      fill: var(--button-disabled-text-color) !important;
    }
  }
}

.box_icon {
  align-items: center;
  display: flex;
  height: 100%;
  justify-content: center;
  width: 100%;
  div {
    height: 160px !important;
    width: 160px !important;

    svg {
      height: 100% !important;
      width: 100% !important;
    }
  }
}

.loader {
  width: 100%;
  & > div {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
  svg {
    margin-left: auto;
    margin-right: auto;
  }
}

.plug {
  &__container {
    align-items: center;

    background-color: var(--white);
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 85px;
    margin-left: auto;
    margin-right: auto;
    text-align: center;

    width: 100%;

    div {
      width: 100% !important;
    }

    svg {
      margin-left: auto;
      margin-right: auto;
    }
  }

  &__loader {
    align-items: center;

    display: flex;
    flex-direction: column;
    justify-content: center;
    left: 50%;
    position: absolute;
    top: 50%;

    transform: translate(-50%, -50%);
    &__container {
      align-items: center;
      height: 100%;
      justify-content: center;
      padding: 40px;
    }
  }

  &__text {
    color: var(--gray-gray-70, #8e97af);
    font: var(--font-title-3-medium);
  }
}

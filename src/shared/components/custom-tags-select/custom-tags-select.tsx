import { FC, memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { IListItem } from '@/shared/ui'
import { TagsSelect } from '../tags-select'
import { useGetOrganizationTagsQuery } from '@/store/services/tags-employees-service'
import { isTagCustom } from '@/shared/types/enums/tag'
import { ITagTarget } from '@/shared/types/store/tag'
import { useTranslation } from 'react-i18next'

type Props = {
  label?: string
  initialSelectedTags?: ITagTarget[]
  handleChangeTags: (tags: ITagTarget[]) => void
  className?: string
  inputBaseClassName?: string
  isListTop?: boolean
}

export const CustomTagsSelect: FC<Props> = memo(
  ({
    label,
    initialSelectedTags,
    handleChangeTags,
    className,
    inputBaseClassName,
    isListTop = false,
  }) => {
    const { t } = useTranslation()
    const [selectedTags, setSelectedTags] = useState<ITagTarget[]>(() => initialSelectedTags || [])
    const initialCustomTagIds = useRef<UUID[] | null>(
      initialSelectedTags ? initialSelectedTags.map(tag => tag.id) : null,
    )

    const { data: tags } = useGetOrganizationTagsQuery({
      limit: 1000,
      offset: 0,
    })

    useEffect(() => {
      handleChangeTags(selectedTags)
    }, [selectedTags])

    const customTags = useMemo(() => {
      if (!tags) return

      return tags?.data?.filter(tag => isTagCustom(tag))
    }, [tags])

    const selectedTagsIds = useMemo(() => {
      return selectedTags.map(tag => tag.id)
    }, [selectedTags])

    const handleChangeTag = useCallback(
      (selectedTag: IListItem) => {
        const { id: selectedTagId } = selectedTag
        const selectedTagsIds = selectedTags.map(tag => tag.id)
        if (selectedTagsIds?.includes(selectedTagId)) {
          return setSelectedTags(oldSelectedTags => [
            ...oldSelectedTags.filter(selectedTag => selectedTag.id !== selectedTagId),
          ])
        }

        setSelectedTags(oldSelectedTags => [
          ...oldSelectedTags,
          {
            id: selectedTagId,
            isNew: Boolean(!initialCustomTagIds.current?.includes(selectedTagId)),
          },
        ])
      },
      [selectedTags],
    )

    return (
      <TagsSelect
        label={label ? label : t('commons:assign_tag')}
        className={className}
        inputBaseClassName={inputBaseClassName}
        handleChange={handleChangeTag}
        tagsList={customTags}
        selectedTags={selectedTagsIds}
        isListTop={isListTop}
      />
    )
  },
)

CustomTagsSelect.displayName = 'CustomTagsSelect'

import React, { FC, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'

import { assignedCoursesApi } from '@/pages/admin/assigned-courses/api'
import { departmentAPI } from 'entities/department'
import { useEmployees, useUserOrganizationId } from '@/entities/employee'
import { useGetOrganizationTagsQuery } from '@/store/services/tags-employees-service'
import { Button, Input, Loader, IListItem, MultiSelect, Select } from '@/shared/ui'
import { Modal } from '@/shared/components'
import {
  getFilteredAssignedCourses,
  getFilteredPhishingCampaigns,
} from '@/shared/helpers/employees'
import { ECourseProgress, ELearning, EPhishingEvent, ERole } from '@/shared/types/enums'
import { toggleArrayValue } from '@/shared/helpers'
import { Checkbox } from '@/shared/ui/checkbox'
import { useGetTranslatePhishingTypeList } from '@/shared/hooks'
import styles from './employees-filter.module.scss'
import { EmployeesFilterProps } from './employees-filter.d'
import { phishingQueries } from '@/entities/phishing'

const cx = classNamesBind.bind(styles)

export const EmployeesFilter: FC<EmployeesFilterProps.Props> = props => {
  const { className } = props

  const { setFilter, defaultValues, filter, setPage, hasFilter, setIsAllSelected } = useEmployees()
  const { t } = useTranslation()

  const [openModal, setOpenModal] = useState(false)
  const userOrganizationId = useUserOrganizationId()

  const { data: assignedCourses, isLoading: isLoadingAssignedCourses } =
    assignedCoursesApi.useGetAssignedCoursesByOrganizationQuery(
      {
        organization_id: userOrganizationId ?? '',
        limit: Number.MAX_SAFE_INTEGER,
      },
      { skip: !openModal },
    )

  const { data: phishing, isLoading: isPhishingLoading } =
    phishingQueries.useGetPhishingCampaignsQuery(undefined, {
      skip: !openModal,
    })
  const { data: tags, isLoading: isOrganizationLoading } = useGetOrganizationTagsQuery(
    {
      limit: 1000,
      offset: 0,
    },
    {
      skip: !openModal,
    },
  )

  const [phishingEvents, setPhishingEvents] = useState<EPhishingEvent[]>(filter.phishingEvents)
  const [localDepartments, setLocalDepartments] = useState<UUID[]>(filter.departments)
  const [localRole, setLocalRole] = useState<ERole | null>(filter.role)
  const [localCourses, setLocalCourses] = useState<UUID[]>(filter.courses)
  const [localPhishing, setLocalPhishing] = useState<UUID[]>(filter.phishing)
  const [localTags, setLocalTags] = useState<UUID[]>(filter.tags)
  const [localLearning, setLocalLearning] = useState<ELearning[]>(filter.learning)
  const [localCourseProgress, setLocalCourseProgress] = useState<ECourseProgress[]>(
    filter.courseProgress,
  )

  const handleFilterClick = () => setOpenModal(prev => !prev)

  const {
    data: departments,
    isLoading,
    error,
  } = departmentAPI.useGetDepartmentsQuery({ limit: Number.MAX_SAFE_INTEGER }, { skip: !openModal })

  const handleChangeValue = <T,>(
    i: IListItem,
    value: T[],
    setValue: React.Dispatch<React.SetStateAction<T[]>>,
  ) => {
    const { id } = i

    toggleArrayValue<T>(id as unknown as T, value, setValue)
  }

  const handleChangeTags = (i: IListItem) => {
    handleChangeValue<UUID>(i, localTags, setLocalTags)
  }
  const handleChangePhishingType = (i: IListItem) => {
    handleChangeValue<EPhishingEvent>(i, phishingEvents, setPhishingEvents)
  }
  const handleChangeLearning = (i: IListItem) => {
    handleChangeValue<ELearning>(i, localLearning, setLocalLearning)
  }
  const handleChangeDepartment = (i: IListItem) => {
    handleChangeValue<string>(i, localDepartments, setLocalDepartments)
  }
  const handleChangeRole = (i: IListItem) => {
    setLocalRole(i.id as ERole)
  }
  const handleChangeCourses = (i: IListItem) => {
    handleChangeValue<UUID>(i, localCourses, setLocalCourses)
  }
  const handleChangePhishing = (i: IListItem) => {
    handleChangeValue<UUID>(i, localPhishing, setLocalPhishing)
  }
  const handleChangeCourseProgress = (i: IListItem) => {
    handleChangeValue<ECourseProgress>(i, localCourseProgress, setLocalCourseProgress)
  }

  const handleReset = () => {
    setPhishingEvents(defaultValues.phishingEvents)
    setLocalDepartments(defaultValues.departments)
    setLocalRole(defaultValues.role)
    setLocalCourses(defaultValues.courses)

    setLocalPhishing(defaultValues.phishing)
    setLocalTags(defaultValues.tags)
    setLocalLearning(defaultValues.learning)
    setLocalCourseProgress(defaultValues.courseProgress)

    setRiskLevelMin(defaultValues.riskLevelMin + '')
    setRiskLevelMax(defaultValues.riskLevelMax + '')

    setFilter(defaultValues)
    setPage(0)
    // setOpenModal(false)
  }

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    setPage(0)
    setFilter({
      phishingEvents,
      departments: localDepartments,
      role: localRole,
      courses: localCourses,
      courseProgress: localCourseProgress,
      phishing: localPhishing,
      learning: localLearning,
      tags: localTags,
      riskLevelMin: +riskLevelMin,
      riskLevelMax: +riskLevelMax,
    })
    setIsAllSelected(false)
    setOpenModal(false)
  }

  const [riskLevelMin, setRiskLevelMin] = useState(`${filter.riskLevelMin || '0'}`)
  const [riskLevelMax, setRiskLevelMax] = useState(`${filter.riskLevelMax || '10'}`)

  const PHISHING_TYPE_LIST = useGetTranslatePhishingTypeList()

  const ROLE_LIST = useMemo(
    () => [
      { id: 'employee', title: t('commons:employee_role') },
      { id: 'operator', title: t('commons:operator') },
      { id: 'content_manager', title: t('commons:content_manager') },
    ],
    [t],
  )

  const COURSE_PROGRESS_LIST = useMemo(
    () => [
      { id: 'NOT_START', title: t('commons:didnt_start') },
      { id: 'COMPLETED', title: t('commons:employee_completed') },
      { id: 'NOT_COMPLETED', title: t('commons:not_completed') },
      { id: 'WITHOUT_COURSE', title: t('commons:without_course') },
    ],
    [t],
  )

  const LEARNING_LIST = useMemo(
    () => [
      { id: 'NORMAL', title: t('commons:normal_plan') },
      { id: 'BEHIND', title: t('commons:behind_plan') },
    ],
    [t],
  )

  const handleChangeInput = (value: string, name: string) => {
    const onChange = name === 'min' ? setRiskLevelMin : setRiskLevelMax

    if (!isNaN(+value)) onChange(`${+value}`)
    if (+value > 10) onChange('10')
  }

  const filteredAssignedCourses = useMemo(
    () => getFilteredAssignedCourses([...(assignedCourses?.data || [])]),
    [assignedCourses?.data],
  )

  const filteredPhishing = useMemo(
    () => getFilteredPhishingCampaigns(phishing?.data),
    [phishing?.data],
  )

  return (
    <>
      <div className={cx('wrapper', className)}>
        {hasFilter && (
          <Button color='gray' size='small' onClick={handleReset}>
            {t('commons:reset')}
          </Button>
        )}
        <Button
          color='gray'
          size='small'
          rightIcon='filter'
          className={cx('filterButton', {
            active: hasFilter,
          })}
          onClick={handleFilterClick}
        >
          {t('commons:filters')}
        </Button>
      </div>

      <Modal active={openModal} setActive={setOpenModal} className={cx('modalWrapper')}>
        {isLoading && <Loader size='56' loading />}
        {!isLoading && error && (
          <div className='error-text'>{t('commons:failed_while_loading_departments')}</div>
        )}
        {!isLoading && !error && departments && (
          <form onSubmit={e => handleSubmit(e)} className={cx('inner')}>
            <div className={cx('innerGrid')}>
              <div>
                <div className={cx('title')}>{t('commons:phishing')}</div>
                <div className={cx('items')}>
                  <MultiSelect
                    label={t('commons:status')}
                    placeholder={t('commons:not_important')}
                    onChange={handleChangePhishingType}
                    list={PHISHING_TYPE_LIST}
                    customValue={phishingEvents}
                  />
                  {isPhishingLoading ? (
                    <MultiSelectLoader label={t('commons:which_newsletter')} />
                  ) : (
                    <MultiSelect
                      searchable
                      label={t('commons:which_newsletter')}
                      placeholder={t('commons:select_mailings')}
                      onChange={handleChangePhishing}
                      list={filteredPhishing}
                      customValue={localPhishing}
                    />
                  )}
                </div>
              </div>
              <div>
                <div className={cx('title')}>{t('commons:courses')}</div>
                <div className={cx('items')}>
                  {isLoadingAssignedCourses ? (
                    <MultiSelectLoader label={t('commons:tags')} />
                  ) : (
                    <MultiSelect
                      searchable
                      label={t('commons:assigned_course')}
                      placeholder={t('commons:select_courses')}
                      onChange={handleChangeCourses}
                      list={filteredAssignedCourses}
                      customValue={localCourses}
                    />
                  )}
                  <MultiSelect
                    label={t('commons:course_progress')}
                    placeholder={t('commons:any')}
                    onChange={handleChangeCourseProgress}
                    list={COURSE_PROGRESS_LIST}
                    customValue={localCourseProgress}
                  />
                </div>
              </div>
            </div>
            <div className={cx('title')}>{t('commons:total')}</div>
            <div className={cx('items', 'common')}>
              <div className={cx('itemsInner')}>
                <MultiSelect
                  searchable
                  label={t('commons:department')}
                  placeholder={t('commons:department')}
                  onChange={handleChangeDepartment}
                  list={departments.data.map(d => ({
                    title: d.title,
                    id: d.id,
                  }))}
                  customValue={localDepartments}
                />
                <Select
                  label={t('commons:roles')}
                  placeholder={t('commons:roles')}
                  list={ROLE_LIST}
                  handleChange={v => handleChangeRole(v)}
                  wrapperClassName={cx('selectWrapper')}
                  value={localRole}
                  isEmptyDefaultValue
                />
              </div>
              <div>
                <div className={cx('itemTitle')}>{t('commons:risk_level')}</div>
                <div className={cx('itemInputs')}>
                  <div>
                    <span>{t('commons:from').toLowerCase()}</span>
                    <Input
                      className={cx('itemInput')}
                      onChange={handleChangeInput}
                      name='min'
                      value={riskLevelMin}
                    />
                  </div>
                  <div>
                    <span>{t('commons:before').toLowerCase()}</span>
                    <Input
                      className={cx('itemInput')}
                      onChange={handleChangeInput}
                      name='max'
                      value={riskLevelMax}
                    />
                  </div>
                </div>
              </div>
              <div>
                {isOrganizationLoading ? (
                  <MultiSelectLoader label={t('commons:tags')} />
                ) : (
                  !!tags?.data?.length && (
                    <MultiSelect
                      placeholder={t('commons:tags')}
                      onChange={handleChangeTags}
                      customValue={localTags}
                      label={t('commons:tags')}
                      list={tags?.data}
                    />
                  )
                )}
              </div>
              <div>
                <div className={cx('itemTitle')}>{t('commons:learning')}</div>
                {LEARNING_LIST.map(i => {
                  const isActive = localLearning.indexOf(i.id as ELearning) !== -1

                  return (
                    <div className={cx('itemCheckbox')} key={`learnign-${i.id}`}>
                      <Checkbox
                        customChecked={isActive}
                        onChange={() => handleChangeLearning(i)}
                        label={<span>{i.title}</span>}
                      />
                    </div>
                  )
                })}
              </div>
            </div>
            <div className={cx('buttonWrapper')}>
              <Button color='gray' onClick={handleReset} type='button'>
                {t('commons:reset')}
              </Button>
              <Button type='submit'>{t('commons:apply')}</Button>
            </div>
          </form>
        )}
      </Modal>
    </>
  )
}

const MultiSelectLoader = ({ label }: { label: string }) => {
  return (
    <div>
      <p className={cx('label')}>{label}</p>
      <Loader className={cx('multiselectLoader')} />
    </div>
  )
}
